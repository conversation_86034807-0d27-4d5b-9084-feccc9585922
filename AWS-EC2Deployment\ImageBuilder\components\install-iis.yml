# AWS Image Builder Component: Complete IIS Server Installation
# This component installs IIS with all required features, configures registry settings, and sets up firewall rules

name: win-server-iis-complete
description: Install and configure IIS with ASP.NET, registry optimizations, and firewall rules for web server deployment
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingIIS
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking for existing IIS installation..."
        $iisFeature = Get-WindowsFeature -Name "IIS-WebServerRole" -ErrorAction SilentlyContinue
        if ($iisFeature -and $iisFeature.InstallState -eq "Installed") {
            Write-Host "IIS is already installed"
            exit 0
        } else {
            Write-Host "IIS is not installed. Proceeding with installation..."
        }

  - name: InstallIISCore
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing IIS Web Server Role and core features..."

        $features = @(
            "IIS-WebServerRole",
            "IIS-WebServer",
            "IIS-CommonHttpFeatures",
            "IIS-HttpErrors",
            "IIS-HttpRedirect",
            "IIS-ApplicationDevelopment",
            "IIS-NetFxExtensibility45",
            "IIS-HealthAndDiagnostics",
            "IIS-HttpLogging",
            "IIS-Security",
            "IIS-RequestFiltering",
            "IIS-Performance",
            "IIS-WebServerManagementTools",
            "IIS-ManagementConsole",
            "IIS-HttpCompressionStatic",
            "IIS-HttpCompressionDynamic",
            "IIS-DirectoryBrowsing",
            "IIS-DefaultDocument",
            "IIS-StaticContent"
        )

        foreach ($feature in $features) {
            Write-Host "Installing feature: $feature"
            Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
        }

  - name: InstallASPNETFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing ASP.NET features..."

        $aspNetFeatures = @(
            "IIS-NetFxExtensibility45",
            "IIS-ASPNET45",
            "IIS-ISAPIExtensions",
            "IIS-ISAPIFilter",
            "IIS-NetFxExtensibility",
            "IIS-ASPNET"
        )

        foreach ($feature in $aspNetFeatures) {
            Write-Host "Installing ASP.NET feature: $feature"
            Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
        }

  - name: ConfigureIISRegistry
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring IIS registry settings..."

        # Configure IIS logging settings
        $iisLogPath = "HKLM:\SOFTWARE\Microsoft\InetStp"
        if (Test-Path $iisLogPath) {
            Set-ItemProperty -Path $iisLogPath -Name "LogFileDirectory" -Value "C:\inetpub\logs\LogFiles" -Type String
        }

        # Configure ASP.NET settings
        $aspNetPath = "HKLM:\SOFTWARE\Microsoft\ASP.NET"
        if (-not (Test-Path $aspNetPath)) {
            New-Item -Path $aspNetPath -Force | Out-Null
        }

        # Configure request filtering
        $requestFilterPath = "HKLM:\SOFTWARE\Microsoft\IIS Extensions\Request Filtering"
        if (-not (Test-Path $requestFilterPath)) {
            New-Item -Path $requestFilterPath -Force | Out-Null
        }
        Set-ItemProperty -Path $requestFilterPath -Name "MaxAllowedContentLength" -Value 30000000 -Type DWord
        Set-ItemProperty -Path $requestFilterPath -Name "MaxUrl" -Value 4096 -Type DWord
        Set-ItemProperty -Path $requestFilterPath -Name "MaxQueryString" -Value 2048 -Type DWord

        Write-Host "IIS registry settings configured"

  - name: ConfigureIISSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring IIS application settings..."

        Import-Module WebAdministration -ErrorAction SilentlyContinue

        # Configure default application pool
        Write-Host "Configuring Default Application Pool..."
        Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
        Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "recycling.periodicRestart.time" -Value "00:00:00"
        Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "processModel.idleTimeout" -Value "00:20:00"
        Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "processModel.maxProcesses" -Value 1

        # Configure default website
        Write-Host "Configuring Default Web Site..."
        Set-ItemProperty -Path "IIS:\Sites\Default Web Site" -Name "serverAutoStart" -Value $true

        # Configure compression
        Set-WebConfigurationProperty -Filter "system.webServer/httpCompression" -Name "directory" -Value "C:\inetpub\temp\IIS Temporary Compressed Files"
        Set-WebConfigurationProperty -Filter "system.webServer/httpCompression" -Name "staticCompressionLevel" -Value 9
        Set-WebConfigurationProperty -Filter "system.webServer/httpCompression" -Name "dynamicCompressionLevel" -Value 4

  - name: ConfigureIISFirewallRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring IIS firewall rules..."

        $webGroup = "IIS Web Server"

        # Allow HTTP traffic (port 80)
        Write-Host "Allowing HTTP traffic (port 80)..."
        New-NetFirewallRule -DisplayName "IIS HTTP Inbound" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Any -Description "Allow HTTP web traffic for IIS"

        # Allow HTTPS traffic (port 443)
        Write-Host "Allowing HTTPS traffic (port 443)..."
        New-NetFirewallRule -DisplayName "IIS HTTPS Inbound" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Any -Description "Allow HTTPS secure web traffic for IIS"

        # Allow IIS Management Service (port 8172)
        Write-Host "Allowing IIS Management Service (port 8172)..."
        New-NetFirewallRule -DisplayName "IIS Management Service" -Group $webGroup -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8172 -Profile Any -Description "Allow IIS Management Service"

        # Enable built-in IIS firewall rules
        Write-Host "Enabling built-in IIS firewall rules..."
        $iisRuleGroups = @(
            "World Wide Web Services (HTTP)",
            "Secure World Wide Web Services (HTTPS)",
            "Web Management Service (HTTP)",
            "Web Management Service (HTTPS)",
            "IIS Management Console"
        )

        foreach ($ruleGroup in $iisRuleGroups) {
            try {
                Enable-NetFirewallRule -DisplayGroup $ruleGroup -ErrorAction SilentlyContinue
                Write-Host "Enabled firewall rule group: $ruleGroup"
            }
            catch {
                Write-Host "Could not enable firewall rule group $ruleGroup : $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

  - name: CreateTestPage
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Creating IIS test page..."

        $serverName = hostname
        $currentDate = Get-Date
        $iisVersion = (Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\InetStp\" -Name "VersionString" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty VersionString)

        $testPageContent = @"
        <!DOCTYPE html>
        <html>
        <head>
            <title>IIS Server - $serverName</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background-color: #0078d4; color: white; padding: 20px; }
                .content { padding: 20px; }
                .info { background-color: #f5f5f5; padding: 15px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>IIS Web Server Ready</h1>
                <p>Server: $serverName</p>
            </div>
            <div class="content">
                <div class="info">
                    <h3>Server Information</h3>
                    <p><strong>Server Name:</strong> $serverName</p>
                    <p><strong>IIS Version:</strong> $iisVersion</p>
                    <p><strong>Build Date:</strong> $currentDate</p>
                    <p><strong>Status:</strong> IIS is running and configured</p>
                </div>
            </div>
        </body>
        </html>
        "@

        $testPagePath = "C:\inetpub\wwwroot\test.html"
        Set-Content -Path $testPagePath -Value $testPageContent -Encoding UTF8
        Write-Host "Test page created at: $testPagePath"

- name: validate
  steps:
  - name: ValidateIISInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating IIS installation..."

        # Check if IIS service is running
        $iisService = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
        if ($iisService -and $iisService.Status -eq "Running") {
            Write-Host "SUCCESS: IIS service (W3SVC) is running"
        } else {
            Write-Error "FAILED: IIS service is not running"
            exit 1
        }

        # Check if default website is accessible
        try {
            $response = Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Host "SUCCESS: Default website is accessible"
            }
        } catch {
            Write-Warning "Default website test failed: $($_.Exception.Message)"
        }

        # Verify firewall rules
        $httpRule = Get-NetFirewallRule -DisplayName "IIS HTTP Inbound" -ErrorAction SilentlyContinue
        $httpsRule = Get-NetFirewallRule -DisplayName "IIS HTTPS Inbound" -ErrorAction SilentlyContinue

        if ($httpRule -and $httpsRule) {
            Write-Host "SUCCESS: IIS firewall rules are configured"
        } else {
            Write-Warning "Some IIS firewall rules may not be configured properly"
        }

        Write-Host "IIS installation and configuration completed successfully"
